#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/bin/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/bin/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/bin/vite.js" "$@"
fi

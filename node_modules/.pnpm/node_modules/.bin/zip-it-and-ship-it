#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/zip-it-and-ship-it/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/zip-it-and-ship-it/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules/@netlify/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@netlify+zip-it-and-ship-it@12.1.1_rollup@4.41.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@netlify/zip-it-and-ship-it/bin.js" "$@"
else
  exec node  "$basedir/../@netlify/zip-it-and-ship-it/bin.js" "$@"
fi

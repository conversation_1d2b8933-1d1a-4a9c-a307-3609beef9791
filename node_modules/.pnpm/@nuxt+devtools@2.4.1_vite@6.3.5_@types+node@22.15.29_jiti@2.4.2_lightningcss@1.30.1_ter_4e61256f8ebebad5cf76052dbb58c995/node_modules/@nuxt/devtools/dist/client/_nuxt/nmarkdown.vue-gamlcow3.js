import{d as o}from"./client-etfulgf7.js";import{p as t,U as a,u as e,a2 as r,ae as s,a9 as d}from"./vendor/json-editor-vue-g7zhhq6q.js";const i=t({__name:"NMarkdown",props:{markdown:{},tag:{}},setup(m){return(n,p)=>n.markdown&&e(o)?.devtools?.renderMarkdown?(r(),a(s(n.tag||"span"),{key:0,class:"n-markdown",innerHTML:e(o).devtools.renderMarkdown(n.markdown)},null,8,["innerHTML"])):(r(),a(s(n.tag||"span"),{key:1,class:"n-markdown",textContent:d(n.markdown)},null,8,["textContent"]))}});export{i as _};

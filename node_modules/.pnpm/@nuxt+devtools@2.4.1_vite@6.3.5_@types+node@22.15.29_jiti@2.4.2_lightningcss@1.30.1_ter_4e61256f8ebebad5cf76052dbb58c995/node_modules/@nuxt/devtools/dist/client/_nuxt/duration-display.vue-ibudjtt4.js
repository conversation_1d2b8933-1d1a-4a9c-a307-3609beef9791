import{p as i,q as u,I as d,a2 as f,Y as l,V as o,aa as p,R as c,a9 as a}from"./vendor/json-editor-vue-g7zhhq6q.js";const m={"ml-1":"","text-xs":"",op50:""},g=i({__name:"DurationDisplay",props:{duration:{},factor:{default:1},color:{type:Boolean,default:!0}},setup(n){const t=n;function s(e){return t.color?e?(e=e*t.factor,e<.5?"text-gray:50":e>1e3?"text-red-400":e>500?"text-orange-400":e>200?"text-yellow-400":""):"text-gray-400":""}const r=u(()=>!t.duration||t.duration<1?["<1","ms"]:t.duration<1e3?[t.duration.toFixed(0),"ms"]:t.duration<1e3*60?[(t.duration/1e3).toFixed(1),"s"]:[(t.duration/1e3/60).toFixed(1),"min"]);return(e,x)=>(f(),d("div",{class:l(s(e.duration))},[o(e.$slots,"before"),p(" "+a(r.value[0]),1),c("span",m,a(r.value[1]),1),o(e.$slots,"after")],2))}});export{g as _};

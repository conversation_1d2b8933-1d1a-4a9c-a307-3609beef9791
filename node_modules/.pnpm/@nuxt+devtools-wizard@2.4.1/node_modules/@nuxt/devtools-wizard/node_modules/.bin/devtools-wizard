#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/devtools-wizard/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/devtools-wizard/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules/@nuxt/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@nuxt+devtools-wizard@2.4.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.mjs" "$@"
else
  exec node  "$basedir/../../cli.mjs" "$@"
fi

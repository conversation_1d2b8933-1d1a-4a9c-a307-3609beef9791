#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/esm/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/dist/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules/glob/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/glob@10.4.5/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../glob/dist/esm/bin.mjs" "$@"
else
  exec node  "$basedir/../glob/dist/esm/bin.mjs" "$@"
fi

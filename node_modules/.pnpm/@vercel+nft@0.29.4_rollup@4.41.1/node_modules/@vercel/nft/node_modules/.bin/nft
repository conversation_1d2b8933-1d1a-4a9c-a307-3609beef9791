#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/nft/out/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/nft/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/nft/out/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/nft/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules/@vercel/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/@vercel+nft@0.29.4_rollup@4.41.1/node_modules:/Users/<USER>/Github/teaspoon-catering/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../out/cli.js" "$@"
else
  exec node  "$basedir/../../out/cli.js" "$@"
fi

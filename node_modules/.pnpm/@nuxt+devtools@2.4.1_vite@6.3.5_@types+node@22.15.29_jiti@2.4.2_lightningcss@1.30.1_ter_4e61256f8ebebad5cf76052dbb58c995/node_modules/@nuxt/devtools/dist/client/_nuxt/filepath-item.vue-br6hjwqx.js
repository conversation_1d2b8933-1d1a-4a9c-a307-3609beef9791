import{w as k,a as c,A as g,a3 as C}from"./f1cbvne4.js";import{p as b,q as B,I as t,a2 as a,R as s,V as N,S as u,F as l,aa as d,a9 as r,Y as I,u as m}from"./vendor/json-editor-vue-g7zhhq6q.js";const S={flex:"~ gap-2 items-center",class:"group"},V=["title"],w={key:0,op50:""},E={key:0,flex:"~ gap1",pr2:"",op0:"","group-hover:op100":""},D=b({__name:"FilepathItem",props:{filepath:{},lineBreak:{type:Boolean},subpath:{type:Boolean},override:{}},setup(f){const p=f,v=k(),h=c(),i=g(),n=B(()=>p.filepath&&i.value?C(p.filepath,i.value.rootDir):{path:p.filepath||""});return(e,o)=>(a(),t("span",S,[s("span",{class:I([e.lineBreak?"":"ws-nowrap of-hidden truncate"]),"font-mono":"",title:e.override||e.filepath},[e.override?(a(),t(l,{key:0},[d(r(e.override),1)],64)):n.value.moduleName?(a(),t(l,{key:1},[s("span",null,r(n.value.moduleName),1),e.subpath?(a(),t("span",w,r(n.value.path.slice(n.value.moduleName.length)),1)):u("",!0)],64)):(a(),t(l,{key:2},[d(r(n.value.path),1)],64))],10,V),N(e.$slots,"default"),e.filepath?(a(),t("div",E,[s("button",{"text-sm":"",op40:"",hover:"op100 text-primary",title:"Open in editor",onClick:o[0]||(o[0]=y=>m(v)(e.filepath))},o[2]||(o[2]=[s("div",{"i-carbon-script-reference":""},null,-1)])),s("button",{"text-sm":"",op40:"",hover:"op100 text-primary",title:"Copy path",onClick:o[1]||(o[1]=y=>m(h)(e.filepath))},o[3]||(o[3]=[s("div",{"i-carbon-copy":""},null,-1)]))])):u("",!0)]))}});export{D as _};

{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*"], "@/*": ["../*"], "~~/*": ["../*"], "@@/*": ["../*"], "nitropack/types": ["../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack/runtime"], "nitropack": ["../node_modules/.pnpm/nitropack@2.11.12/node_modules/nitropack"], "defu": ["../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../node_modules/.pnpm/h3@1.15.3/node_modules/h3"], "consola": ["../node_modules/.pnpm/consola@3.4.2/node_modules/consola"], "ofetch": ["../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@unhead/vue": ["../node_modules/.pnpm/@unhead+vue@2.0.10_vue@3.5.16_typescript@5.8.3_/node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_ter_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/.pnpm/@vue+runtime-core@3.5.16/node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/.pnpm/@vue+compiler-sfc@3.5.16/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/.pnpm/unplugin-vue-router@0.12.0_vue-router@4.5.1_vue@3.5.16_typescript@5.8.3___vue@3.5.16_typescript@5.8.3_/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/.pnpm/@nuxt+schema@3.17.4/node_modules/@nuxt/schema"], "nuxt": ["../node_modules/.pnpm/nuxt@3.17.4_@parcel+watcher@2.5.1_@types+node@22.15.29_db0@0.3.2_ioredis@5.6.1_lightnin_ad3efa013d1f13e06ffaa568dfd1c543/node_modules/nuxt"], "vite/client": ["../node_modules/.pnpm/vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_terser@5.40.0_yaml@2.8.0/node_modules/vite/client"], "#shared": ["../shared"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../node_modules/.pnpm/nuxt@3.17.4_@parcel+watcher@2.5.1_@types+node@22.15.29_db0@0.3.2_ioredis@5.6.1_lightnin_ad3efa013d1f13e06ffaa568dfd1c543/node_modules/nuxt/dist/core/runtime/nitro/utils/paths"], "#unhead/composables": ["../node_modules/.pnpm/nuxt@3.17.4_@parcel+watcher@2.5.1_@types+node@22.15.29_db0@0.3.2_ioredis@5.6.1_lightnin_ad3efa013d1f13e06ffaa568dfd1c543/node_modules/nuxt/dist/head/runtime/composables/v3"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_ter_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools/runtime/server", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../node_modules/.pnpm/nuxt@3.17.4_@parcel+watcher@2.5.1_@types+node@22.15.29_db0@0.3.2_ioredis@5.6.1_lightnin_ad3efa013d1f13e06ffaa568dfd1c543/node_modules/nuxt/node_modules", "../node_modules/.pnpm/@nuxt+devtools@2.4.1_vite@6.3.5_@types+node@22.15.29_jiti@2.4.2_lightningcss@1.30.1_ter_4e61256f8ebebad5cf76052dbb58c995/node_modules/@nuxt/devtools/node_modules", "../node_modules/.pnpm/@nuxt+telemetry@2.6.6_magicast@0.3.5/node_modules/@nuxt/telemetry/node_modules", "../dist"]}